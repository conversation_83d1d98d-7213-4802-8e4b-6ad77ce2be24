import { useQuery } from "@tanstack/react-query";
import { Line<PERSON>hart, Line, ResponsiveContainer, Tooltip } from "recharts";
import { formatPrice } from "@/utils/urgency-display";
import { format } from "date-fns";
import type { PriceHistory } from "@shared/schema";

interface PriceSparklineProps {
  scraperId: string;
}

interface ChartDataPoint {
  date: string;
  price: number;
  timestamp: Date;
}

export default function PriceSparkline({ scraperId }: PriceSparklineProps) {
  const { data: priceHistory = [], isLoading, error } = useQuery<PriceHistory[]>({
    queryKey: [`/api/scrapers/${scraperId}/history`],
    enabled: !!scraperId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  if (isLoading) {
    return (
      <div className="w-full h-8 bg-muted/20 animate-pulse rounded" />
    );
  }

  if (error) {
    return (
      <div className="w-full h-8 flex items-center justify-center text-xs text-muted-foreground">
        Failed to load
      </div>
    );
  }

  if (!priceHistory.length) {
    return (
      <div className="w-full h-8 flex items-center justify-center text-xs text-muted-foreground">
        No price data
      </div>
    );
  }

  // Filter to last 90 days and prepare chart data
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  const chartData: ChartDataPoint[] = priceHistory
    .filter(entry => {
      const entryDate = new Date(entry.timestamp);
      return entryDate >= ninetyDaysAgo && entry.price && !isNaN(parseFloat(entry.price));
    })
    .map(entry => ({
      date: format(new Date(entry.timestamp), 'MMM dd'),
      price: parseFloat(entry.price),
      timestamp: new Date(entry.timestamp),
    }))
    .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

  if (!chartData.length) {
    return (
      <div className="w-full h-8 flex items-center justify-center text-xs text-muted-foreground">
        No recent data
      </div>
    );
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white border border-gray-300 rounded-lg px-2 py-1 shadow-lg" style={{ fontSize: '11px' }}>
          <p className="font-medium text-gray-900">{formatPrice(data.price.toString())}</p>
          <p className="text-gray-600">{format(data.timestamp, 'MMM dd, yyyy')}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="w-full h-8 mt-1">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={chartData}
          margin={{ top: 2, right: 2, left: 2, bottom: 2 }}
          style={{ background: 'transparent' }}
        >
          <Line
            type="monotone"
            dataKey="price"
            stroke="#000000"
            strokeWidth={1.5}
            dot={false}
            activeDot={{ r: 2, stroke: "#000000", strokeWidth: 1, fill: "#000000" }}
            connectNulls={false}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={{ stroke: '#000000', strokeWidth: 1, strokeDasharray: '3 3' }}
            offset={15}
            position={{ y: -10 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
